<?php

function processCx($oid)
{
	global $DB;
	$d = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
	$b = $DB->get_row("select hid,user,pass from qingka_wangke_order where oid='{$oid}' ");
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$b["hid"]}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	$ip = $a["ip"];
	$user = $b["user"];
	$pass = $b["pass"];
	$kcname = $d["kcname"];
	$school = $d["school"];
	$pt = $d["noun"];
	$kcid = $d["kcid"];

	//YYY进度接口
    if ($type == "yyy") {

		$data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $pt, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "yid" => $d['yid']);
		$dx_rl = $a["url"];
        $dx_url = "$dx_rl/api/getorder";

		$result = get_url($dx_url,$data);
		$result = json_decode($result, true);

		if ($result["code"] == "200") {
			foreach ($result["data"]["list"] as $res) {
				$yid = $res["id"];
				$remarks = $res["status"];
				$process = 0;
				if (isset($res["train"])){
					$kcname = $res["train"];
				}
				$code=$res["code"];
				if ($code==107){
					$status = "队列中";
				}elseif ($code==103){
					$status = "异常";
				}elseif ($code==102){
					$status = "已完成";
					$process = 100;
				}elseif ($code==101){
					$status = "已退款";
				}else{
					$status = "进行中";
					preg_match('/^\d+(\.\d+)?/', $remarks, $matches);
					if (isset($matches[0])) {
						$process = $matches[0];
					} else {
						$process = 50;
					}
				}
				$process="$process%";

				$b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "status_text" => $status, "process" => $process, "remarks" => $remarks);
			}
		} else {
			$b[] = array("code" => -1, "msg" => $result["message"]);
		}
		return $b;
	}

	//8090edu进度接口
	if ($type == "8090edu") {
		// 智能Token管理 - 优先使用缓存的token，失效时自动刷新
		$token = $a["token"];
		$need_refresh_token = false;

		// 检查是否有缓存的token
		if (empty($token)) {
			$need_refresh_token = true;
		} else {
			// 验证token是否有效
			$test_url = "{$a["url"]}/api/user/balance";
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $test_url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 10);
			curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
			$test_result = curl_exec($ch);
			$curl_error = curl_error($ch);
			curl_close($ch);

			if ($curl_error || !$test_result) {
				$need_refresh_token = true;
			} else {
				$test_result_array = json_decode($test_result, true);
				if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
					$need_refresh_token = true;
				}
			}
		}

		// 如果需要刷新token，重新登录
		if ($need_refresh_token) {
			$login_data = array(
				"username" => $a["user"],
				"password" => $a["pass"]
			);

			$login_url = "{$a["url"]}/api/auth/login";

			// 使用curl发送JSON请求
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $login_url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 30);
			curl_setopt($ch, CURLOPT_POST, true);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
			curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
			$login_result = curl_exec($ch);
			curl_close($ch);

			$login_result = json_decode($login_result, true);

			if (!$login_result || $login_result["code"] != 200) {
				$error_msg = isset($login_result["message"]) ? $login_result["message"] : "登录失败";
				return [array("code" => -1, "msg" => $error_msg)];
			}

			$token = $login_result["data"]["token"];

			// 更新数据库中的token缓存
			$DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}', endtime = NOW() WHERE hid = '{$a["hid"]}'");
		}

		$b = [];
		$local_yid = $d['yid']; // 本地订单的yid
		$found_order = false;

		// 8090教育API已更新，现在可以直接通过订单ID查询
		// 优先使用订单ID进行精确查询
		if (!empty($local_yid)) {
			// 方法1：直接通过订单ID查询（如果8090教育支持单个订单查询API）
			// 方法2：查询用户订单列表，然后精确匹配订单ID
			$order_url = "{$a["url"]}/api/order/list?username=" . urlencode($user) . "&page=1&pageSize=50&sortField=createTime&sortOrder=descend";
		} else {
			// 如果没有订单ID，查询该用户的订单列表
			$order_url = "{$a["url"]}/api/order/list?username=" . urlencode($user) . "&page=1&pageSize=20&sortField=createTime&sortOrder=descend";
		}
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $order_url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_TIMEOUT, 30);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: {$token}"));
		$order_result = curl_exec($ch);
		$curl_error = curl_error($ch);
		curl_close($ch);

		if ($curl_error) {
			return [array("code" => -1, "msg" => "网络请求失败: " . $curl_error)];
		}

		$order_result = json_decode($order_result, true);

		if ($order_result && $order_result["code"] == 200 && isset($order_result["data"]["list"])) {
			$target_order = null;

			// 8090教育API已更新，现在优先使用订单ID进行精确匹配
			foreach ($order_result["data"]["list"] as $order) {
				// 获取订单信息
				$orderId = isset($order["orderId"]) ? strval($order["orderId"]) : "";
				$username = isset($order["username"]) ? $order["username"] : "";

				// 优先级1：精确订单ID匹配（最可靠）
				if (!empty($local_yid) && strval($local_yid) == $orderId && $username == $user) {
					$target_order = $order;
					$found_order = true;
					break; // 找到精确匹配，直接退出
				}

				// 优先级2：如果没有订单ID，使用用户名匹配最新订单
				if (empty($local_yid) && $username == $user && !$target_order) {
					$target_order = $order; // 由于按时间倒序排列，第一个匹配的就是最新的
					$found_order = true;
				}
			}

			// 如果找到匹配的订单
			if ($found_order && $target_order) {
				$order = $target_order;

				$orderId = isset($order["orderId"]) ? strval($order["orderId"]) : "";
				$status = isset($order["status"]) ? $order["status"] : "未知";
				$courseName = isset($order["courseName"]) ? $order["courseName"] : "";
				$username = isset($order["username"]) ? $order["username"] : "";
				$password = isset($order["password"]) ? $order["password"] : "";
				$courseInfo = isset($order["courseInfo"]) ? $order["courseInfo"] : "";
				$createTime = isset($order["createTime"]) ? $order["createTime"] : "";
				$updateTime = isset($order["updateTime"]) ? $order["updateTime"] : "";

				// 重要：使用数据库中的用户名和课程名，确保WHERE条件匹配
				// 这样系统的更新SQL才能正确匹配到订单
				$db_user = $user;  // 使用数据库中的用户名
				$db_kcname = $kcname; // 使用数据库中的课程名

				$process = 0;

				// 根据状态设置进度
				switch ($status) {
					case "已完成":
						$process = 100;
						break;
					case "已退款":
						$process = 0;
						break;
					case "进行中":
						// 尝试从courseInfo中提取进度信息
						if (preg_match('/(\d+(?:\.\d+)?)%/', $courseInfo, $matches)) {
							$process = floatval($matches[1]);
						} else {
							$process = 50; // 默认进度
						}
						break;
					case "待处理":
					case "队列中":
						$process = 10;
						break;
					case "异常":
						$process = 0;
						break;
					default:
						$process = 20;
						break;
				}

				// 只提取课程信息作为详细信息
				$detailed_info = "";
				if (!empty($courseName)) {
					$detailed_info = "课程: " . $courseName;
				} else {
					// 如果没有courseName，使用数据库中的课程名作为备用
					$detailed_info = $db_kcname;
				}

				$b[] = array(
					"code" => 1,
					"msg" => "查询成功",
					"yid" => $orderId,
					"kcname" => $db_kcname, // 使用数据库中的课程名，确保WHERE条件匹配
					"name" => $detailed_info, // 系统期望的字段，使用详细的订单信息
					"user" => $db_user, // 使用数据库中的用户名，确保WHERE条件匹配
					"pass" => $password,
					"status_text" => $status,
					"process" => $process . "%",
					"remarks" => "8090教育订单 - " . $detailed_info . " - 更新时间: " . $updateTime,
					"createTime" => $createTime,
					"updateTime" => $updateTime,
					// 系统期望的时间字段，8090教育没有这些信息，设置为空
					"kcks" => "", // 课程开始时间
					"kcjs" => "", // 课程结束时间
					"ksks" => "", // 考试开始时间
					"ksjs" => ""  // 考试结束时间
				);
			}
		}

		// 如果没有找到匹配的订单
		if (!$found_order) {
			$b[] = array(
				"code" => -1,
				"msg" => "未找到匹配的订单，订单ID: {$local_yid}，用户: {$user}",
				"yid" => $local_yid,
				"kcname" => $kcname, // 使用数据库中的课程名
				"name" => $kcname, // 系统期望的字段
				"user" => $user, // 使用数据库中的用户名
				"pass" => $pass,
				"status_text" => "未知",
				"process" => "0%",
				"remarks" => "请检查订单信息是否正确，或稍后重试",
				// 系统期望的时间字段
				"kcks" => "",
				"kcjs" => "",
				"ksks" => "",
				"ksjs" => ""
			);
		}

		return $b;
	}

    if ($type == "29") {
        $data = array("username" => $user);
        $dx_rl = $a["url"];
        $dx_url = "$dx_rl/api.php?act=chadan";
        $result = get_url($dx_url, $data);
        $result = json_decode($result, true);
        if ($result["code"] == "1") {
        foreach ($result["data"] as $res) {
        $yid = $res["id"];
        $kcname = $res["kcname"];
        $status = $res["status"];
        $process = $res["process"];
        $remarks = $res["remarks"];
        $kcks = $res["courseStartTime"];
        $kcjs = $res["courseEndTime"];
        $ksks = $res["examStartTime"];
        $ksjs = $res["examEndTime"];
        $b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "ksks" => $ksks, "ksjs" => $ksjs, "status_text" => $status, "process" => $process, "remarks" => $remarks);
        }
        } else {
        $b[] = array("code" => -1, "msg" => $result["msg"]);
        }
        return $b;
    }
     if ($type == "bdkj") {
       $data = array("oid" => $d['yid'],"uid" => $a["user"], "key" => $a["pass"]);
    $dx_rl = $a["url"];
    $dx_url  = "$dx_rl/api.php?act=chadanoid";
    $result = get_url($dx_url, $data);
    $result = json_decode($result, true);
    if ($result["code"] == "1") {
    foreach ($result["data"] as $res) {
    $yid = $res["id"];
    $kcname = $res["kcname"];
    $status = $res["status"];
    $process = $res["process"];
    $remarks = $res["remarks"];
    $kcks = $res["courseStartTime"];
    $kcjs = $res["courseEndTime"];
    $ksks = $res["examStartTime"];
    $ksjs = $res["examEndTime"];
    $b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "ksks" => $ksks, "ksjs" => $ksjs, "status_text" => $status, "process" => $process, "remarks" => $remarks);
    }
    } else {
    $b[] = array("code" => -1, "msg" => "查询失败,请联系管理员");
    }
    return $b;
    }

    //易教育进度接口
    if ($type == "jxjy") {
        // 智能Token管理 - 优先使用缓存的token，失效时自动刷新
        $token = $a["token"];
        $need_refresh_token = false;

        // 检查是否有缓存的token
        if (empty($token)) {
            $need_refresh_token = true;
        } else {
            // 验证token是否有效
            $test_url = "{$a["url"]}/api/user/info";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $test_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer {$token}"));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            $test_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$test_result) {
                $need_refresh_token = true;
            } else {
                $test_result_array = json_decode($test_result, true);
                if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
                    $need_refresh_token = true;
                }
            }
        }

        // 如果需要刷新token，重新登录
        if ($need_refresh_token) {
            $login_data = array(
                "username" => $a["user"],
                "password" => $a["pass"]
            );

            $login_url = "{$a["url"]}/api/login";

            // 使用curl发送JSON请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $login_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            $login_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$login_result) {
                $b[] = array("code" => -1, "msg" => "登录失败：网络错误 - " . $curl_error);
                return $b;
            }

            $login_result_array = json_decode($login_result, true);

            if (!$login_result_array || !isset($login_result_array["code"]) || $login_result_array["code"] != 200) {
                $error_msg = isset($login_result_array["message"]) ? $login_result_array["message"] : "登录失败";
                $b[] = array("code" => -1, "msg" => $error_msg);
                return $b;
            }

            $token = $login_result_array["data"]["token"];

            // 更新数据库中的token
            $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$a["hid"]}'");
        }

        // 修复：使用正确的字段名获取项目编号
        $websiteNumber = $pt; // $pt 就是项目编号

        // 构建查询数据 - 根据您提供的API信息修复
        $query_data = array(
            "pageSize" => 10,
            "pageNum" => 1,
            "status" => "all",
            "name" => "",
            "username" => "",
            "websiteNumber" => ""
        );

        // 优先使用订单号查询（这是最准确的方式）
        if (!empty($d['yid'])) {
            $query_data["orderNumber"] = $d['yid'];
        } else {
            // 如果没有订单号，使用用户名查询
            $query_data["username"] = $user;
            $query_data["websiteNumber"] = $websiteNumber;
        }

        // 发送查询请求
        $query_url = "{$a["url"]}/api/order/list";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $query_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($query_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer {$token}"
        ));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $query_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error || !$query_result) {
            $b[] = array("code" => -1, "msg" => "查询失败：网络错误 - " . $curl_error);
            return $b;
        }

        $query_result_array = json_decode($query_result, true);

        if (!$query_result_array || !isset($query_result_array["code"]) || $query_result_array["code"] != 200) {
            $error_msg = isset($query_result_array["message"]) ? $query_result_array["message"] : "查询失败";
            $b[] = array("code" => -1, "msg" => $error_msg);
            return $b;
        }

        // 解析订单状态 - 根据您提供的API信息修复状态映射
        $status_mapping = array(
            0 => '队列中',
            1 => '已完成',  // 根据您的API信息，status=1表示已完成
            2 => '进行中',
            3 => '异常',
            4 => '正在售后',
            5 => '售后完成',
            6 => '人工处理',
            7 => '已退款',
            8 => '暂停中',
            9 => '已暂停'
        );

        $found_order = false;
        if (isset($query_result_array["data"]["list"]) && is_array($query_result_array["data"]["list"])) {
            foreach ($query_result_array["data"]["list"] as $order) {
                // 修复：根据您提供的API信息改进订单匹配逻辑
                $order_username = isset($order["username"]) ? $order["username"] : "";
                $order_number = isset($order["number"]) ? $order["number"] : "";

                // 多重匹配条件：优先使用订单号匹配，其次用户名匹配
                $order_number_match = (!empty($d['yid']) && $order_number == $d['yid']);
                $username_match = (empty($d['yid']) && $order_username == $user);

                if ($order_number_match || $username_match) {
                    $found_order = true;

                    // 🔥 关键修复：保持原有的 yid，不要被 API 返回的值覆盖
                    $yid = $d['yid']; // 使用数据库中的原始订单号

                    // 如果数据库中的 yid 为空或为 "1"，且 API 返回了有效的订单号，才使用 API 的值
                    if ((empty($d['yid']) || $d['yid'] == '1') && !empty($order_number) && $order_number != '1') {
                        $yid = $order_number;
                    }

                    $status_code = isset($order["status"]) ? $order["status"] : 0;
                    $status = isset($status_mapping[$status_code]) ? $status_mapping[$status_code] : "未知状态";

                    // 修复：根据您提供的API信息处理进度
                    $progress = isset($order["progress"]) ? $order["progress"] : "0%";

                    // 根据您的API信息，progress字段可能是文字描述，如"学习完成"
                    if (is_string($progress)) {
                        // 处理文字描述的进度
                        if (strpos($progress, '完成') !== false || strpos($progress, '已完成') !== false) {
                            $progress = "100%";
                        } elseif (strpos($progress, '进行') !== false || strpos($progress, '学习中') !== false) {
                            $progress = "50%";
                        } elseif (strpos($progress, '开始') !== false || strpos($progress, '队列') !== false) {
                            $progress = "10%";
                        } elseif (is_numeric($progress)) {
                            // 如果是数字，确保在0-100范围内并添加%
                            $progress = floatval($progress);
                            if ($progress > 100) {
                                $progress = 100;
                            } elseif ($progress < 0) {
                                $progress = 0;
                            }
                            $progress = $progress . "%";
                        } elseif (strpos($progress, '%') === false && !empty($progress)) {
                            // 如果是其他文字描述，保持原样
                            // 但同时设置一个默认的百分比进度
                            $progress = $progress; // 保持原文字
                        } else {
                            $progress = "0%";
                        }
                    } else {
                        $progress = "0%";
                    }

                    $remarks = isset($order["remark"]) ? $order["remark"] : "";
                    $order_kcname = $kcname; // 使用原始课程名

                    // 如果有课程信息，使用API返回的课程名
                    if (isset($order["courses"]) && is_array($order["courses"]) && !empty($order["courses"])) {
                        $course_names = array();
                        foreach ($order["courses"] as $course) {
                            if (isset($course["name"])) {
                                $course_names[] = $course["name"];
                            }
                        }
                        if (!empty($course_names)) {
                            $order_kcname = implode(", ", $course_names);
                        }
                    }

                    // 根据您的API信息添加更多详细信息
                    $update_date = isset($order["updateDate"]) ? $order["updateDate"] : "";
                    $website_name = isset($order["websiteName"]) ? $order["websiteName"] : "";
                    $raw_progress = isset($order["progress"]) ? $order["progress"] : "";

                    $b[] = array(
                        "code" => 1,
                        "msg" => "查询成功",
                        "yid" => $yid,
                        "kcname" => $order_kcname,
                        "user" => $user,
                        "pass" => $pass,
                        "status_text" => $status,
                        "process" => $progress,
                        "remarks" => "易教育同步 - 原始进度: {$raw_progress} - 更新时间: {$update_date}",
                        "raw_progress" => $raw_progress,
                        "website_name" => $website_name,
                        "update_date" => $update_date
                    );
                }
            }
        }

        // 如果没有找到匹配的订单
        if (!$found_order) {
            $b[] = array("code" => -1, "msg" => "未找到匹配的订单信息");
        }

        return $b;
    }

	else {
       $b[] = array("code" => -1, "msg" => "查询失败,请联系管理员");
	}

	return $b;

?>